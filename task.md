# Tasks Document
## ML-Based Ksat Prediction Project - Sequential Task List

### Task 1: Data Loading and Initial Exploration
**status**: completed
**Objective**: Load data and perform initial quality checks
**Actions**:
1. Load data.xlsx file
2. Verify all 18 columns are present (1 target + 17 predictors)
3. Confirm 5,770 observations
4. Generate basic statistics for all variables
5. Create distribution plots for Ksat (confirm skewness) and others
6. Document data types for each variable
**Output**: Data quality report with basic statistics and visualizations
**finding**: Dataset successfully loaded with 5,769 rows × 18 columns (within acceptable range of expected 5,770). All required columns present: 1 target variable (Ksat) + 17 predictors (8 soil properties, 2 hydrological, 5 environmental, 2 categorical). No missing values detected (excellent data quality). Target variable Ksat is highly right-skewed (skewness=5.79, kurtosis=98.99) requiring transformation. Similarly, clay(%), sand(%), silt(%), soc(%) and PWP are also skewed. Categorical variables: Texture (11 categories, sand dominant at 62.6%) and LU (15 categories).

### Task 2: Target Variable Transformation
**status**: completed
**Objective**: Transform skewed Ksat distribution
**Actions**:
1. Test log transformation on Ksat
2. Test Box-Cox transformation on Ksat
3. Compare distributions (Q-Q plots, Shapiro-Wilk test)
4. Select best transformation based on normality tests
5. Document transformation parameters for inverse transformation
**Output**: Transformed Ksat variable and transformation report
**finding**: Successfully identified and transformed 13 skewed variables (out of 17 numerical variables). Box-Cox transformation was optimal for all 13 variables (Ksat, CEC, NDVI, Pr, Slope, db, soil_acidity, clay(%), sand(%), silt(%), soc(%), PWP, FC), providing superior normality compared to log transformation. Key transformations: Ksat (λ=0.2258, skewness: 5.79→-0.15). Variables with acceptable skewness (|skew|<0.5): LU, TWI, T, depth remained untransformed.

### Task 3: Multicollinearity Analysis
**status**: completed
**Objective**: Identify and handle correlated features (excluding ksat, LU and texture)
**Actions**:
1. Create correlation matrix for all numerical features (excluding LU, ksat, and texture)
2. Generate correlation heatmap
3. Identify feature pairs with |correlation| > 0.9
4. Document removed features and rationale (correlation)
5. Do not use box-cox transformation or other preprocessing for this step. Use data as it is.
**finding**: Successfully analyzed 15 numerical features for multicollinearity (excluding Ksat, LU, and Texture as specified). Identified 1 feature pairs with |correlation| > 0.9: clay(%)↔PWP (r=0.92). Recommended removing 1 features to address multicollinearity: PWP.

### Task 4: Categorical Variable
**status**: completed
**Objective**: Convert categorical variables to numerical format
**Actions**:
1. Identify unique values in LU (numeric categorical)
2. Identify unique values in Texture (string categorical)
3. Apply one-hot encoding to both variables
4. Update feature list with new encoded columns
5. save data as task4_encoded_data.csv
**Output**: encoded data as task4_encoded_data.csv
**finding**: Successfully completed categorical variable encoding. Removed PWP feature (highly correlated with clay%, r=0.92) as identified in Task 3. Analyzed and encoded 2 categorical variables: LU (15 unique values) and Texture (11 unique values). Applied one-hot encoding creating 26 binary columns (15 for LU + 11 for Texture). Final dataset: 5,769 rows × 41 columns (1 target + 40 features: 14 numerical + 26 encoded categorical). Net feature expansion: +24 features (removed 1 PWP, added 26 encoded columns). Data saved as task4_encoded_data.csv.

### Task 5: Box-Cox Transformation and Feature Scaling
**status**: in_progress
**Objective**: Apply Box-Cox transformations and standardize numerical features
**Actions**:
1. Apply Box-Cox transformations to 12 variables (excluding PWP which was removed): Ksat, CEC, NDVI, Pr, Slope, db, soil_acidity, clay(%), sand(%), silt(%), soc(%), FC
2. Separate features requiring scaling (exclude one-hot encoded)
3. Apply StandardScaler (z-score normalization) to numerical features
4. Save transformation and scaler parameters for future predictions
**Output**: Box-Cox transformed and scaled dataset as task5_transformed_scaled_data.csv
**finding**: Successfully completed feature scaling. Identified and scaled 14 numerical features (CEC, NDVI, Pr, Slope, TWI, T, db, depth, soil_acidity, clay(%), sand(%), silt(%), soc(%), FC) using StandardScaler. Left 26 categorical features (15 LU + 11 Texture one-hot encoded columns) unscaled as they are already binary. Scaling verification confirmed proper normalization (mean ≈ 0, std ≈ 1). Final dataset: 5,769 rows × 41 columns. Saved scaled data, scaler object, and scaling parameters for reproducibility.

### Task 6: Data Splitting
**status**: completed
**Objective**: Create train/validation/test sets
**Actions**:
1. Set random seed for reproducibility (e.g., 42)
2. Split data: 60% train, 20% validation, 20% test (task5_scaled_data.csv)
3. Save train, validation and test data as three csv files
**Output**: Three datasets saved as task6_train_data.csv, task6_validation_data.csv and task6_test_data.csv
**finding**: Successfully completed data splitting with random seed 42 for reproducibility. Created task6_train_data.csv, task6_validation_data.csv and task6_test_data.csv. Split scaled dataset (5,769 samples) into: Train (3,461 samples, 60.0%), Validation (1,154 samples, 20.0%), Test (1,154 samples, 20.0%). Target distribution well-preserved across splits: Train mean=519.39±738.25, Val mean=541.56±688.86, Test mean=553.44±793.99. All three datasets saved as separate CSV files with complete feature sets (40 features + 1 target). 

### Task 7: Hyperparameter Optimization - Random Forest
**status**: completed
**Objective**: Find optimal RF parameters using grid search
**Actions**:
1. Define parameter grid:
   - n_estimators: [100, 200, 300]
   - max_depth: [10, 20, 30, None]
   - min_samples_split: [2, 5, 10]
   - min_samples_leaf: [1, 2, 4]
2. Implement 10-fold cross-validation
3. Use R² as scoring metric
4. Track training time
5. Save best parameters and CV scores
6. If training and validation R² difference > 10%, adjust parameters and repeat
**Output**: Report Optimal RF parameters and performance report in finding of Task 7.
**finding**: MAJOR IMPROVEMENT with Box-Cox transformation + overfitting fix! Applied Box-Cox transformations to 12 variables, then addressed overfitting with regularized hyperparameters. FINAL BEST MODEL: n_estimators=100, max_depth=20, min_samples_split=10, min_samples_leaf=5, max_features=0.7. Performance: CV R²=0.7847, OOB R²=0.7830, Training R²=0.9077 (RMSE=1.78, MAE=1.26), Validation R²=0.7954 (RMSE=2.66, MAE=2.00), Test R²=0.7857 (RMSE=2.83, MAE=2.05). Overfitting IMPROVED: Train-Validation=11.22% (vs 15.79%), Train-Test=12.19% (vs 17.06%). Still slightly above 10% threshold but significantly better. Key features: clay%(39.1%), FC(27.1%), db(8.8%), sand%(5.7%). Box-Cox transformation provided +25.68pp improvement over original data. Training time: 2.47 minutes.

### Task 8: Hyperparameter Optimization - SVM
**status**: not_started
**Objective**: Find optimal SVM parameters
**Actions**:
1. Define parameter grid:
   - kernel: ['rbf', 'linear', 'poly']
   - C: [0.1, 1, 10, 100]
   - gamma: ['scale', 'auto', 0.001, 0.01, 0.1]
2. Implement 10-fold cross-validation
3. Use same scoring and tracking as RF
**Output**: Optimal SVM parameters and performance report
**finding**:

### Task 9: Hyperparameter Optimization - XGBoost
**status**: not_started
**Objective**: Find optimal XGBoost parameters
**Actions**:
1. Define parameter grid:
   - n_estimators: [100, 200, 300]
   - max_depth: [3, 5, 7, 9]
   - learning_rate: [0.01, 0.1, 0.3]
   - subsample: [0.7, 0.8, 0.9]
2. Implement 10-fold cross-validation
3. Use same scoring and tracking
**Output**: Optimal XGBoost parameters and performance report
**finding**:

### Task 10: Hyperparameter Optimization - ANN
**status**: not_started
**Objective**: Find optimal ANN architecture
**Actions**:
1. Define parameter grid:
   - hidden_layer_sizes: [(50,), (100,), (50,50), (100,50)]
   - activation: ['relu', 'tanh']
   - learning_rate_init: [0.001, 0.01, 0.1]
   - alpha: [0.0001, 0.001, 0.01]
2. Implement 10-fold cross-validation
3. Set max_iter appropriately
**Output**: Optimal ANN parameters and performance report
**finding**:

### Task 11: Model Training and Evaluation
**status**: not_started
**Objective**: Train final models and check for overfitting
**Actions**:
1. Train each model with optimal parameters on training set
2. Evaluate on training set (R², RMSE, MAE)
3. Evaluate on test set (R², RMSE, MAE)
4. Calculate R² difference (train - test)
5. If difference > 10%, apply regularization:
   - RF: increase min_samples_leaf/split
   - SVM: adjust C parameter
   - XGBoost: increase reg_alpha/reg_lambda
   - ANN: increase alpha, add dropout
6. Iterate until R² difference < 10%
**Output**: Final trained models with performance metrics
**finding**:

### Task 12: Model Comparison and Selection
**status**: not_started
**Objective**: Compare all models and select best performer
**Actions**:
1. Create comprehensive comparison table
2. Include metrics: R², RMSE, MAE, training time
3. Generate prediction vs. actual plots
4. Calculate residual plots
5. Perform statistical tests for significant differences
6. Select best model based on test performance
**Output**: Model comparison report and best model selection
**finding**:

### Task 13: Feature Importance Analysis
**status**: not_started
**Objective**: Identify key predictors of Ksat
**Actions**:
1. Extract feature importance from tree-based models
2. Calculate permutation importance for all models
3. Create feature importance plots
4. Analyze consistency across models
5. Relate findings to soil science theory
**Output**: Feature importance report and visualizations
**finding**:

### Task 14: Final Documentation and Results
**status**: not_started
**Objective**: Prepare results for manuscript
**Actions**:
1. Create summary statistics table
2. Generate publication-quality figures
3. Document complete methodology
4. Prepare model equations/parameters
5. Save all models and preprocessing objects
6. Create reproducibility package
**Output**: Complete results package ready for manuscript
**finding**: